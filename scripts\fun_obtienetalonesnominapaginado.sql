-- Function: fun_obtienetalonesnominapaginado(integer, date, integer, integer)

-- DROP FUNCTION fun_obtienetalonesnominapaginado(integer, date, integer, integer);

CREATE OR REPLACE FUNCTION fun_obtienetalonesnominapaginado(IN integer, IN date, IN integer, IN integer)
  RETURNS TABLE(num_empleado integer, nom_empleado character varying, fec_nomina date, des_correo character varying, des_archivopdf bytea, clv_tipo integer, clv_empresa integer) AS
$BODY$
DECLARE 
	iTipoCorreo ALIAS FOR $1;
	dFechaNomina ALIAS FOR $2;
	iLimit ALIAS FOR $3;
	iOffset ALIAS FOR $4;
	iTotaltalones INTEGER;
BEGIN
	return QUERY
	SELECT a.num_empleado,a.nom_empleado,a.fec_nomina,a.des_correo,a.des_archivopdf,a.clv_tipo,a.clv_empresa
	FROM his_talonesempleadosmail a
	WHERE a.clv_tipo = iTipoCorreo
		AND (a.fec_nomina = dFechaNomina OR dFechaNomina = '19000101')
		AND a.clv_estatus = 0
	ORDER BY a.num_empleado
	offset iOffset
	limit iLimit;
END
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER
  COST 100
  ROWS 1000;
ALTER FUNCTION fun_obtienetalonesnominapaginado(integer, date, integer, integer)
  OWNER TO syspruebasadmon;
