# Accesos personal
HOSTPERSONAL=*************
USERPERSONAL=U2FsdGVkX1+yeRh4YE11MFshZgLfIqbxu1Nthy+LwD0=
CLAVEPERSONAL=U2FsdGVkX1+6cMgDGBR/1gPfyEvInVwJUUJjFkSEMFrStwCsHcrwjwepFgZ9q69G0R/zPfOP4TJqykLvoTfi+g==
BDPERSONAL=U2FsdGVkX1/St3mjxv0tT9PUkZhLcnJuA1h7O2UWhMU=
PUERTOPERSONAL=1433
CLAVESECRETA=$4p13n10tal0n3sn0m1n4c00p3l$

# Accesos amazon
URLCORREOAMAZON=email-smtp.us-west-1.amazonaws.com
USUARIOCORREOAMAZON=AKIAW3MEDXT42KWVLM7I
CLAVECORREOAMAZON=BFrIthJjttmhG9Kq+V6w6ihHLxo4n7F71+cg1rFPRDKV
PUERTOCORREOAMAZON=465
CORREOSALIDAAMAZON=<EMAIL>

# sapipadmon
IDACCESOSCORREO=188
IDACCESOSADMINISTRACION=143

# configuraciones correo
PUERTOCORREO=25

# Puertos
PUERTOPOSTGRES=5432

# Ruta instalación node
RUTANODE=/usr/bin/

# Numero de nucleos con los que trabajara el proceso de envio de correos
NUMERONUCLEOS=7

# Fechas para envio de correo formato mes-dia(MM-DD)
FECHAUTILIDADES=05-20
FECHAAGUINALDO=11-20

# Tipos de correo que usa la aplicacion
TIPOCORREONOMINA=1
TIPOCORREOFONDO=2
TIPOCORREOGASOLINA=3
TIPOCORREOUTILIDADES=4
TIPOCORREOAGUINALDO=5

# Codigos de salida para aplicación secundaria
CODIGOCORRECTO=0
CODIGOERROR=1
CODIGOPARCIAL=2

# Número de correos enviados por bloque
CORREOSPORBLOQUE=100

# Claves categorias
CLAVECREDENCIALESBDADMINISTRACION=1014
CLAVECREDENCIALESBDPERSONAL=1015

# URL y variavles para obtener el token y consultar una cateoria
URLGETTOKEN=http://************:20027/authtoken/getToken
SITIBUNDUS=SISTEMAFINANZAS
UNDIVAGO=localhost
USERTYPE=1

# URL y variables para consultar una categoria
URLGETCREDENCIALES=http://************:3002/api/v1/game
CATEGORY=OBTENERCREDENCIALES
NAME=REMEDIACIONES
TYPE=OFA
APLICACION=ApiEnvioTalones
APLICATIONPERSONAL=ApiAdminSI
RUTAVALIDACION=http://localhost:20027/authtoken/validateToken

HTTP_PROXY=http://***********:8080

# Numero de reintentos para los bloques que dan timeout por el proxy
NUMREINTENTOS=5
