-- Function: fun_obtienetotaltalonesnomina(integer, date)

-- DROP FUNCTION fun_obtienetotaltalonesnomina(integer, date);

CREATE OR REPLACE FUNCTION fun_obtienetotaltalonesnomina(
    integer,
    date)
  RETURNS integer AS
$BODY$
DECLARE 
	iTipoCorreo ALIAS FOR $1;
	dFechaNomina ALIAS FOR $2;
	iTotaltalones INTEGER;
BEGIN
	SELECT COUNT(a.num_empleado) INTO iTotaltalones 
	FROM his_talonesempleadosmail a
	WHERE a.clv_tipo = iTipoCorreo
	AND (a.fec_nomina = dFechaNomina or dFechaNomina = '19000101')
	AND a.clv_estatus = 0;
	
	RETURN iTotaltalones AS Total; 
END
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER
  COST 100;
ALTER FUNCTION fun_obtienetotaltalonesnomina(integer, date)
  OWNER TO syspruebasadmon;
