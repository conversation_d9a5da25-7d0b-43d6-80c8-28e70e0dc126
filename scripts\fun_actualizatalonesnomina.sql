-- Function: fun_actualizatalonesnominaXML(XML)

-- DROP FUNCTION fun_actualizatalonesnominaXML(XML);

CREATE OR REPLACE FUNCTION fun_actualizatalonesnominaXML(xml)
  RETURNS void AS
$BODY$
DECLARE
	xEmpleados ALIAS FOR $1;
BEGIN

	DROP TABLE IF EXISTS tmpTalonesEmpleados;
	create temporary table tmpTalonesEmpleados (
		num_empleado integer,
		clv_empresa integer,
		clv_tipo integer,
		fec_nomina date
	);

	INSERT INTO tmpTalonesEmpleados (num_empleado, clv_empresa, clv_tipo, fec_nomina)
	select 
		(xpath('num_empleado/text()', datos))[1]::TEXT::INT,
		(xpath('clv_empresa/text()', datos))[1]::TEXT::INT,
		(xpath('TipoCorreo/text()', datos))[1]::TEXT::INT,
		(xpath('fec_nomina/text()', datos))[1]::TEXT::date
	FROM  unnest(xpath('/Empleados/Empleado', xEmpleados)) datos;

	UPDATE his_talonesempleadosmail
	 SET clv_estatus = 1,
	     fec_envio = NOW()
	FROM tmpTalonesEmpleados 
	WHERE tmpTalonesEmpleados.num_empleado = his_talonesempleadosmail.num_empleado 
	 AND tmpTalonesEmpleados.clv_empresa = his_talonesempleadosmail.clv_empresa 
	 AND tmpTalonesEmpleados.clv_tipo = his_talonesempleadosmail.clv_tipo 
	 AND tmpTalonesEmpleados.fec_nomina = his_talonesempleadosmail.fec_nomina;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER
  COST 100;
ALTER FUNCTION fun_actualizatalonesnominaXML(XML)
  OWNER TO syspruebasadmon;