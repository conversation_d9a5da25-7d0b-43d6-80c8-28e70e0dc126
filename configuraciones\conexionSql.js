import claseConexion from 'mssql';

const datosConfiguracionBD = {
	pool: {
		max: 10,
		min: 0,
		idleTimeoutMillis: 30000,
	},
	options: {
		encrypt: true,
		trustServerCertificate: true
	}
};

/**
 * Método para consultar los datos de conexion a un servidor
 * @param {String} idConfiguracionBD identificador de los datos de conexion a consultar
 *
 * @returns {Promise<import('mssql').IResult<any>>} Resultado del query
 */
export const consultaConfiguracionBD = async (clavesAccesosBD, idConfiguracionBD) => {
	try {
		const pool = await claseConexion.connect({ ...datosConfiguracionBD, ...clavesAccesosBD });
		const request = pool.request();
		request.input('NClave', idConfiguracionBD);
		const { recordset } = await request.execute("SAPACCESOSDATOS");
		return recordset[0];
	} catch (error) {
		throw new Error(error);
	}
};