CREATE OR REPLACE FUNCTION "public"."fun_obtienetalonesnomina"(int4, int4, date)
  RETURNS TABLE("num_empleado_talon" int4, "nom_empleado_talon" varchar, "fec_nomina_talon" date, "des_correo_talon" varchar, "des_archivopdf_talon" bytea, "clv_tipo_talon" int4, "clv_empresa_talon" int4, "clv_bloque_talon" int4) AS $BODY$
DECLARE
	iTipoCorreo ALIAS FOR $1;
	iOpcion ALIAS FOR $2;
	dFechaNomina ALIAS FOR $3;
	rReg<PERSON>ros RECORD;
	rFechas RECORD;
	iTotalBloques INTEGER;
	iBloque INTEGER;
	iNomina INTEGER;
	dFecha DATE;
BEGIN
	IF (iOpcion = 0) THEN
		/*DELETE FROM mov_talonesempleadosmail
		USING stmpempleado20170930 a
		WHERE mov_talonesempleadosmail.num_empleado = a.num_empleado;

		DELETE FROM mov_talonesempleadosmail
		USING stmpempleado20170930_1 a
		WHERE mov_talonesempleadosmail.num_empleado = a.num_empleado;*/
		
		CREATE TEMPORARY TABLE tmpfechas
		(
			fec_nomina DATE NOT NULL DEFAULT '19000101',
			clv_bloque INTEGER NOT NULL DEFAULT 0,
			num_total INTEGER NOT NULL DEFAULT 0
		) ON COMMIT DROP;

		INSERT INTO tmpfechas(fec_nomina)
		SELECT DISTINCT fec_nomina 
		FROM mov_talonesempleadosmail 
		WHERE clv_tipo = iTipoCorreo;

		IF( (SELECT COUNT(fec_nomina) FROM tmpfechas) > 0) THEN
			FOR rFechas IN SELECT fec_nomina FROM tmpfechas ORDER BY fec_nomina
			LOOP
				iBloque := 1;
				iTotalBloques := COALESCE((SELECT COUNT(*)/100+CASE WHEN COUNT(*)%100.00 > 0 THEN 1 ELSE 0 END FROM mov_talonesempleadosmail WHERE clv_tipo = iTipoCorreo AND fec_nomina = rFechas.fec_nomina),0);

				--Actualiza la cantidad de bloques generados para un fecha y un valor identificador de la fecha
				UPDATE tmpfechas SET clv_bloque = iTotalBloques
				WHERE fec_nomina = rFechas.fec_nomina;

				--Recorre los registros para asignarle un bloque
				WHILE(iBloque <= iTotalBloques)
				LOOP
					INSERT INTO his_talonesempleadosmail(num_empleado,nom_empleado,fec_nomina,des_correo,des_archivopdf,clv_tipo,clv_empresa,clv_bloque,clv_estatus)
					SELECT a.num_empleado,a.nom_empleado,a.fec_nomina,a.des_correo,a.des_archivopdf,a.clv_tipo,a.clv_empresa,iBloque,0
					FROM mov_talonesempleadosmail a
					WHERE a.clv_tipo = iTipoCorreo
					AND a.fec_nomina = rFechas.fec_nomina
					ORDER BY a.num_empleado
					LIMIT 100 OFFSET 0;

					DELETE FROM mov_talonesempleadosmail 
					USING his_talonesempleadosmail b
					WHERE mov_talonesempleadosmail.clv_tipo = b.clv_tipo
					AND mov_talonesempleadosmail.fec_nomina = b.fec_nomina
					AND mov_talonesempleadosmail.clv_empresa = b.clv_empresa
					AND mov_talonesempleadosmail.num_empleado = b.num_empleado
					AND mov_talonesempleadosmail.fec_nomina = rFechas.fec_nomina
					AND b.clv_tipo = iTipoCorreo
					AND b.clv_bloque = iBloque;

					iBloque := iBloque + 1;
				END LOOP;

				iBloque := COALESCE((SELECT MAX(clv_bloque) FROM his_talonesempleadosmail WHERE clv_tipo = iTipoCorreo AND fec_nomina = rFechas.fec_nomina),0)+1;
			END LOOP;

			--Retorna la información acumulada
			FOR rRegistros IN SELECT fec_nomina, clv_bloque FROM tmpfechas ORDER BY fec_nomina
			LOOP
				num_empleado_talon := 0;
				nom_empleado_talon := '';
				fec_nomina_talon := rRegistros.fec_nomina;
				des_correo_talon := '';
				des_archivopdf_talon := '';
				clv_tipo_talon := 0;
				clv_empresa_talon := 0;
				clv_bloque_talon := rRegistros.clv_bloque;

				RETURN NEXT;
			END LOOP;
		ELSE
			dFecha := COALESCE((SELECT MAX(fec_nomina) FROM his_talonesempleadosmail),'19000101');

			IF(dFecha != '19000101') THEN
				IF (TO_CHAR(dFecha,'MMdd') = '0520') THEN
					--Inserta la fecha del 15 de mayo, cuando la fecha maxima es la fecha de reparto.
					INSERT INTO tmpfechas(fec_nomina)
					SELECT DATE(TO_CHAR(CURRENT_DATE,'yyyy')||'0515');
				ELSIF (TO_CHAR(dFecha,'MMdd') = '1130') THEN
					--Inserta la fecha del 20 de noviembre de aguinaldo, cuando la fecha maxima es el 30 de noviembre
					INSERT INTO tmpfechas(fec_nomina)
					SELECT DATE(TO_CHAR(CURRENT_DATE,'yyyy')||'1120');
				END IF;

				--Inserta la fecha maxima de la tabla his_talonesempleadosmail
				INSERT INTO tmpfechas(fec_nomina)
				SELECT dFecha;

				--Obtiene el total de bloques que contiene la fecha
				UPDATE tmpfechas SET clv_bloque = COALESCE((SELECT MAX(b.clv_bloque) FROM his_talonesempleadosmail b WHERE b.clv_tipo = iTipoCorreo AND b.fec_nomina = tmpfechas.fec_nomina), 0);

				--Obtiene el total de registros a los cuales se les enviará correo por fecha nomina
				UPDATE tmpfechas SET num_total = (SELECT COUNT(b.*) FROM his_talonesempleadosmail b WHERE b.clv_tipo = iTipoCorreo AND b.fec_nomina = tmpfechas.fec_nomina AND clv_estatus != 1);

				--Borra las fechas que no contienen registros pendientes de envio de correo
				DELETE FROM tmpfechas WHERE num_total = 0;
			END IF;

			--Retorna la información acumulada
			FOR rRegistros IN SELECT fec_nomina, clv_bloque FROM tmpfechas ORDER BY fec_nomina
			LOOP
				num_empleado_talon := 0;
				nom_empleado_talon := '';
				fec_nomina_talon := rRegistros.fec_nomina;
				des_correo_talon := '';
				des_archivopdf_talon := '';
				clv_tipo_talon := 0;
				clv_empresa_talon := 0;
				clv_bloque_talon := rRegistros.clv_bloque;

				RETURN NEXT;
			END LOOP;
		END IF;
	ELSE		
		FOR rRegistros IN SELECT a.num_empleado,a.nom_empleado,a.fec_nomina,a.des_correo,a.des_archivopdf,a.clv_tipo,a.clv_empresa,a.clv_bloque
				  FROM his_talonesempleadosmail a
				  WHERE a.clv_tipo = iTipoCorreo
				  AND a.fec_nomina = dFechaNomina
				  AND a.clv_bloque = iOpcion
				  AND a.clv_estatus != 1
				  ORDER BY a.num_empleado
		LOOP
			num_empleado_talon := rRegistros.num_empleado;
			nom_empleado_talon := rRegistros.nom_empleado;
			fec_nomina_talon := rRegistros.fec_nomina;
			des_correo_talon := rRegistros.des_correo;
			des_archivopdf_talon := rRegistros.des_archivopdf;
			clv_tipo_talon := rRegistros.clv_tipo;
			clv_empresa_talon := rRegistros.clv_empresa;
			clv_bloque_talon := rRegistros.clv_bloque;

			RETURN NEXT;
		END LOOP;
	END IF;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER;