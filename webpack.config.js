const path = require('path');
const nodeExternals = require('webpack-node-externals');
const TerserPlugin = require('terser-webpack-plugin');

const mode = process.env.NODE_ENV || 'development';

module.exports = {
  stats: 'normal',
  entry: {
    "api-envio-talones": "./index.js",
    workers: "./utilidades/trabajadores.js",
  },
  target: 'node',
  mode: mode,
  devtool: 'source-map',
  output: {
    path: path.resolve(__dirname, 'build'),
    filename: '[name].js',
    publicPath: 'build/',
  },
  externals: [nodeExternals()],
  optimization: {
    minimize: mode !== 'development',
    minimizer: [new TerserPlugin()],
  },
  plugins: [
  ],
  module: {
    rules: [
      {
        test: /\.js$/,
        use: 'babel-loader',
        exclude: /node_modules/,
      },
    ],
  },
};
