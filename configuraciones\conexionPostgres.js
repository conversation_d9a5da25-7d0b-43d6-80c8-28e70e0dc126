import { Pool } from 'pg';

const config = {
    max: 5,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
    client_encoding: 'SQL_ASCII'
}

/**
 * Función que crea una conexión a un bd de postgresql
 * @param {*} datosConfiguracionBD configuraciones de la conexion a la bd
 * @returns Instancia de la bd
 */
export const crearConexionBD = async (datosConfiguracionBD) => {
    const conexion = new Pool({
        ...config,
        ...datosConfiguracionBD,
    });

    conexion.on('error', () => {
        logger.error('Ha ocurrido un error inesperado con la conexción a PostgreSQL');
        process.exit(-1);
    });

    await conexion.connect();

    return conexion;
}

/**
 * Función para ejecutar un query en bd progresql
 * @param {*} instanciaBD Intancia de la bd
 * @param {*} consulta Query a ejecutar
 * @param {*} parametros Parametros de la consulta a ejecutar
 */
export const EjecutarConsultaParametros = async (instanciaBD, consulta, parametros, error = false) => {
    try {
        return await instanciaBD.query(consulta, parametros);
    } catch (error) {
        throw new Error(error);
    }
}