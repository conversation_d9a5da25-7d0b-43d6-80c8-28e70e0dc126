import BodyParser from 'body-parser';
import cors from 'cors';
import Compression from 'compression';
import headers from './middlewares/headers';
import logErrores from './middlewares/logErrores';
import talones from './controladores/talones';

export default (app) => {

  app.use((req,res,next) => {
    console.log(`${new Date().toISOString()}-${req.method} ${req.url} - IP: ${req.ip}`);
    next();
  });

  app.use(Compression());
  app.use(cors({ origin: true, credentials: true }));
  app.use(BodyParser.urlencoded({ limit: '5mb', extended: true }));
  app.use(BodyParser.json({ limit: '5mb', extended: true }));

  // MIDDLEWARE para Cabeceros que serán validos para todas las peticiones
  app.use(headers);

  // Configuración de HSTS
  app.use((req, res, next) => {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    next();
  });
  
  // rutas endpoint
  app.use('/api/v1/talones', talones);

  app.use((err, req, res, next) => {
    const statusCode = err.statusCode || 501;
    const mensaje = 'Error en el servidor';
    res.status(statusCode).json({ error: mensaje });
  });

  // MIDDLEWARE para cachar errores y generar el log
  app.use(logErrores);

  
};
