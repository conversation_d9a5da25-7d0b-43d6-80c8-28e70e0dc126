-- Function: fun_obtienebloquestalonesnomina(integer, date)

-- DROP FUNCTION fun_obtienebloquestalonesnomina(integer, date);

CREATE OR REPLACE FUNCTION fun_obtienebloquestalonesnomina(integer, date)
  RETURNS TABLE(clv_bloque integer) AS 
$BODY$
DECLARE 
	iTipoCorreo ALIAS FOR $1;
	dFechaNomina ALIAS FOR $2;
BEGIN
	return QUERY
	SELECT DISTINCT a.clv_bloque
	FROM his_talonesempleadosmail a
	WHERE a.clv_tipo = iTipoCorreo
	AND (a.fec_nomina = dFechaNomina or dFechaNomina = '19000101')
	AND a.clv_estatus = 0
	ORDER BY a.clv_bloque; 
END
$BODY$
LANGUAGE plpgsql VOLATILE SECURITY DEFINER;