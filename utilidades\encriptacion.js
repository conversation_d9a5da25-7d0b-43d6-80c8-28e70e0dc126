// Importar la biblioteca crypto
import CryptoJ<PERSON> from "crypto-js";

/**
 * 
 * @param {*} textoEncriptar 
 * @returns el texto encriptado
 */
export const Encriptar = (textoEncriptar) => {
    return CryptoJS.AES.encrypt(textoEncriptar, process.env.CLAVESECRETA).toString();
}

export const Desencriptar = (textoEncriptado) => {
    const bytesDesencriptados = CryptoJS.AES.decrypt(textoEncriptado, process.env.CLAVESECRETA);
    return bytesDesencriptados.toString(CryptoJS.enc.Utf8);
}