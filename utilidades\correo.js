import nodemailer from 'nodemailer';
import { logging } from './logs';
import { PuertosTLS } from '../configuraciones/constantes';
import { mensajes } from '../configuraciones/constantes';

class EmailSender {
  /**
   * Contrcutor para la clase correo
   * @param {*} configuraciones Datos para configurar el servidor de correo
   */
  constructor(configuraciones) {

    const seguridad = PuertosTLS.includes(parseInt(process.env.PUERTOCORREOAMAZON, 10));
    const esDesarrollo = process.env.NODE_ENV === 'development';

    try {
      this.transporter = nodemailer.createTransport({
        host: process.env.URLCORREOAMAZON,
        port: process.env.PUERTOCORREOAMAZON,
        secure: seguridad,
        auth: {
          user: process.env.USUARIOCORREOAMAZON,
          pass: process.env.CLAVECORREOAMAZON
        },
        debug: esDesarrollo,
        logger: esDesarrollo,
        pool: true,
        maxConnections: 1,
        proxy: process.env.HTTP_PROXY,
      });
      this.mailOptions = {
        from: process.env.CORREOSALIDAAMAZON,
      };
    } catch (error) {
      logging(true, `Error Proceso envio talones: `, error);
      throw new Error(error);
    }
  }

  /**
   * 
   * @param {*} destinatario Correo al que se enviara el talon
   * @param {*} asunto Titulo del correo
   * @param {*} html Contenido del mensaje
   * @param {*} archivo Documento adjunto al correo
   * @param {*} informacion Informacion del empleado 
   * @returns 
   */
  enviarCorreo(destinatario, asunto, html, archivo, informacion) {
    try {
      const opcionesCorreo = {
        from: this.mailOptions.from,
        to: destinatario,
        subject: asunto,
        html,
        attachments: archivo
      };

      return new Promise((resolve, reject) => {
        this.transporter.sendMail(opcionesCorreo, (error, info) => {
          if (error) {
            logging(true, `Error Envio Correo Proceso envio talones: `, error);
            if (error.message == mensajes.ErorTimeOutProxy) {
              reject(error);
            } else {
              resolve({ ...informacion, error: true, info });
            }
          } else {
            resolve({ ...informacion, info });
          }
        });
      });
    } catch (error) {
      logging(true, `Error Proceso envio talones: `, error);
    }
  }
}

export default EmailSender;
