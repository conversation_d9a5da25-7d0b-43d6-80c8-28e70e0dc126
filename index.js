import claseCliente from 'http';
import 'regenerator-runtime';
import dotenv from 'dotenv';
dotenv.config();

import Express from 'express';
import './configuraciones/env';
import Router from './router';

const port = process.env.PORT || 50413;
const instanciaExpres = Express();
const server = claseCliente.createServer(instanciaExpres);

Express.Router();
Router(instanciaExpres);
console.log(`Servicio iniciado; Env: ${process.env.NODE_ENV}; Puerto: ${port}`);
server.listen(port, '0.0.0.0');