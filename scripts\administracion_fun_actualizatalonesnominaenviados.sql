-- Function: fun_actualizatalonesnominaenviados()

-- DROP FUNCTION fun_actualizatalonesnominaenviados();

CREATE OR REPLACE FUNCTION fun_actualizatalonesnominaenviados()
  RETURNS void AS
$BODY$
DECLARE
BEGIN
	UPDATE his_talonesempleadosmail
	SET
		clv_estatus = 1,
		fec_envio = NOW()
	FROM tmp_his_talonesempleadosmail tht
	WHERE tht.num_empleado = his_talonesempleadosmail.num_empleado 
		AND tht.clv_empresa = his_talonesempleadosmail.clv_empresa
		AND tht.clv_tipo = his_talonesempleadosmail.clv_tipo
		AND tht.fec_nomina = his_talonesempleadosmail.fec_nomina;
		
	DELETE FROM tmp_his_talonesempleadosmail;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER;