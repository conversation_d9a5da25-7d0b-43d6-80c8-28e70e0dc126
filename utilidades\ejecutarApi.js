import axios from "axios";
import { application } from "express";

const {
    SITIBUNDUS: sitibundus,
    UNDIVAGO: undivago,
    USERTYPE: usertype,
    CATEGORY: category,
    NAME: name,
    TYPE: type,
    APLICACION,
    RUTAVALIDACION: rutaValidacion
} = process.env;

const ObtenerToken = () => {
    return axios.post(process.env.URLGETTOKEN, {
        sitibundus,
        undivago,
        usertype: parseInt(usertype, 10)
    });
}

export const ObtenerCredencialesBD = async (clave, aplicacion = APLICACION) => {
    try {

        const { data: { id_token } } = await ObtenerToken();

        return axios.post(process.env.URLGETCREDENCIALES,
            {
                category,
                name,
                type,
                aplicacion,
                categorias: {
                    categoria: clave
                },
                rutaValidacion
            },
            {
                headers: {
                    Authorization: `Bearer ${id_token}`
                }
            });
    } catch (error) {
        throw new Error(error);
    }
}