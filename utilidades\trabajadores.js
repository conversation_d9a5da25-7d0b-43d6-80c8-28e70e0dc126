import cluster from 'cluster';
import { logging } from './logs';
import { EnvioCorreoTalones } from './funcionesTalones';

// variables globales
var ResultadosEnvioCorreos = {
  Enviados: 0,
  Errores: 0,
  CorreosNoValidos: 0,
  NumTimeOutProxy: 0,
  BloquesErrorTimeOut: []
};

try {
  console.log('Iniciando proceso de workers');

  let error = false;
  let correosFallidos = false;

  if (cluster.isPrimary || cluster.isMaster) {
    console.log('proceso de workers del if');

    //Evento para saber cuando los trabajdores terminan
    cluster.on('exit', async (worker, codigoSalida, signal) => {
      if (parseInt(codigoSalida) == parseInt(process.env.CODIGOERROR)) error = true;
      if (parseInt(codigoSalida) == parseInt(process.env.CODIGOPARCIAL)) correosFallidos = true;
      if (Object.keys(cluster.workers).length == 0) {

        // Envia el concentrado de los datos del envio de correos al proceso principal
        console.log(JSON.stringify({ FinProceso: true, Datos: ResultadosEnvioCorreos }));

        setTimeout(() => {
          /**
          * codigoSalidaAplicacion = 0 Es cuando todo funciona de manera correcta
          * codigoSalidaAplicacion = 1 Es cuando no se envió ningún correo
          * codigoSalidaAplicacion = 2 Es cuando se envian correos parcialmente
          */
          if (error && !correosFallidos) {
            process.exit(parseInt(process.env.CODIGOERROR, 10));
          }
          else if (error || correosFallidos) {
            process.exit(parseInt(process.env.CODIGOPARCIAL, 10));
          }
          else {
            process.exit(parseInt(process.env.CODIGOCORRECTO, 10));
          }
        }, 5000);
      }
    });

    cluster.on('message', async (worker, datos) => {
      try {
        const { Enviados, Errores, CorreosNoValidos, NumTimeOutProxy, BloquesErrorTimeOut } = JSON.parse(datos);
        ResultadosEnvioCorreos.Enviados += Enviados;
        ResultadosEnvioCorreos.Errores += Errores;
        ResultadosEnvioCorreos.CorreosNoValidos += CorreosNoValidos;
        ResultadosEnvioCorreos.NumTimeOutProxy += NumTimeOutProxy;
        ResultadosEnvioCorreos.BloquesErrorTimeOut = ResultadosEnvioCorreos.BloquesErrorTimeOut.concat(BloquesErrorTimeOut);
      } catch (error) {
        logging(true, "Error Proceso envio talones: ", error);
      }
    });


    //Inicializa N workers dependiendo de los nucleos
    for (let i = 0; i < process.env.NUMERONUCLEOS; i++) {
      cluster.fork();
    }

    //Eventos del proceso
    process.stdin.setEncoding('utf8');
    process.stdin.on('data', async (datos) => {
      console.log('recibiendo datos', datos);
      logging(false, "Proceso envio talones: ", 'Aplicación secundaria recibe información.');
      // convierte el string a un objeto
      datos = JSON.parse(datos);
      // variables
      const { BloquesTalones } = datos;
      let posicionTrabajador = 1;
      // obtiene el número de talones que ejecutara cada proceso
      let limit = Math.floor(BloquesTalones.length / process.env.NUMERONUCLEOS);
      //Manda información a un proceso para que empiece a trabajar
      for (const id in cluster.workers) {
        let bloquesProcesar = (posicionTrabajador == process.env.NUMERONUCLEOS) ? BloquesTalones : BloquesTalones.slice(0, limit);
        cluster.workers[id].send(JSON.stringify({ ...datos, BloquesProcesar: bloquesProcesar }));
        (posicionTrabajador == process.env.NUMERONUCLEOS) ? BloquesTalones.splice(0, BloquesTalones.length) : BloquesTalones.splice(0, limit);
        posicionTrabajador++;
      }
    });
  } else if (cluster.isWorker) {
    console.log(`Worker ${process.pid} iniciando`);
    process.on('message', async (datos) => {
      try {
        console.log(`Worker ${process.pid} recibido`);
        const respuesta = await EnvioCorreoTalones(datos, process.pid);
        process.send(JSON.stringify(respuesta));
        process.exit((respuesta.Errores > 0) ? process.env.CODIGOPARCIAL : process.env.CODIGOCORRECTO);
      } catch (error) {
        process.send(JSON.stringify(error));
        setTimeout(() => { process.exit(process.env.CODIGOERROR); }, 5000);
      }
    });

    process.on('exit', (code, signal) => {
      process.exit(parseInt(code, 10));
    });
  }
} catch (error) {
  logging(true, "Error Proceso envio talones: ", error);
  setTimeout(() => { process.exit(process.env.CODIGOERROR); }, 5000);
}