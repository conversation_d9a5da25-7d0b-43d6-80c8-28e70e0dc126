import objXml from 'object-to-xml';
import moment from 'moment';
import fs from 'fs';

import { crearConexionBD, EjecutarConsultaParametros } from '../configuraciones/conexionPostgres';
import { GuardarXml, logging } from './logs';
import Correo from './correo';

import { mensajes, regexCorreo } from '../configuraciones/constantes';

/**
 * Inserta los talones de la ejecución anterior que no se pudieron insertar en la tmp_his_talonesempleadosmail
 * @param {*} conexion Instancia de la bd
 */
export const InsertaTemporalHisTalones = async (conexion) => {
    try {
        let promesas = [];
        const ruta = './logs/xml/';
        fs.mkdirSync(ruta, {recursive: true});
        const archivosXML = fs.readdirSync(ruta);

        for (const archivoXML of archivosXML) {
            try {
                let xml = fs.readFileSync(`${ruta}/${archivoXML}`, 'utf-8');

                // Verificar que el XML tenga una estructura básica válida
                if (!xml || !xml.trim().startsWith('<?xml') || !xml.includes('<Empleados>')) {
                    logging(true, `XML inválido en archivo ${archivoXML}`, "Estructura XML no válida");
                    continue; // Saltar este archivo y continuar con el siguiente
                }

                // Sanitizar el XML para prevenir inyección SQL de segundo orden
                // Esto es una medida adicional de seguridad, aunque los parámetros ya están siendo usados correctamente
                xml = xml.replace(/'/g, "''"); // Escapar comillas simples para PostgreSQL

                let parametros = [xml];
                const consultaCrearTmpHisTalonesEmpleadosEmail = `SELECT fun_actualizatalonesnominaXML($1)`;
                promesas.push(
                    new Promise(async (resolve) => {
                        conexion.query(consultaCrearTmpHisTalonesEmpleadosEmail, parametros).then(() => {
                            resolve({ archivoXML });
                        }).catch((error) => {
                            logging(true, "Error al intentar actualizar xml de talones con error: ", error);
                            resolve({ archivoXML, error: true });
                        })
                    })
                );
            } catch (validationError) {
                logging(true, `Error al validar XML del archivo ${archivoXML}`, validationError);
                // Registrar el error pero continuar con el siguiente archivo
                continue;
            }
        }

        let resultados = await Promise.all(promesas);
        if (resultados.length > 0) {
            for (const { archivoXML } of resultados.filter((resultado) => !resultado.error)) {
                fs.unlinkSync(`${ruta}/${archivoXML}`)
            }
        }

    } catch (error) {
        logging(true, "Error al intentar guardar en la tabla tmp_his_talonesempleadosmail", error);
    }
};

/**
/**
 * @param {*} datos Datos que se procesaran
 * @param {*} idProceso Idenficador del proceso que se esta ejecutando
 * @returns Retorna la cantidad de correos que tuvieron error o no se enviaron
 */
export const EnvioCorreoTalones = (datos, idProceso) => {
    return new Promise(async (resolve) => {

        let respuestaCorreos = {
            Enviados: 0,
            Errores: 0,
            CorreosNoValidos: 0,
            NumTimeOutProxy: 0,
            BloquesErrorTimeOut: []
        };

        try {

            logging(false, `Proceso envio talones: `, `Proceso: ${idProceso} Inicia envio de correo.`);

            // convierte el string a un objeto
            datos = JSON.parse(datos);
            const { DatosConexionAdministracion, TipoCorreo, FechaNomina, BloquesProcesar } = datos;
            // Crea conexión a administración
            logging(false, `Proceso envio talones: `, `Proceso: ${idProceso} Crea conexión a administración.`);
            const conexionAdministracion = await crearConexionBD(DatosConexionAdministracion);

            let parametros = [];

            // Consulta los mensajes para los correos
            logging(false, `Proceso envio talones: `, `Proceso: ${idProceso} Consulta los mensajes para los correos.`);
            let tiposCorreo = [
                process.env.TIPOCORREONOMINA,
                process.env.TIPOCORREOFONDO,
                process.env.TIPOCORREOGASOLINA,
                process.env.TIPOCORREOUTILIDADES,
                process.env.TIPOCORREOAGUINALDO
            ];

            parametros = [tiposCorreo.join(',')];
            const consultaMensajesCorreo = `
            SELECT
                clv_tiponomina,
                des_asunto,
                des_saludo,
                des_mensaje,
                des_despedida,
                des_automatico
            FROM fun_obtener_mensajescomprobantes($1)`;

            const { rows: mensajesCorreo } = await EjecutarConsultaParametros(conexionAdministracion, consultaMensajesCorreo, parametros, true);

            respuestaCorreos = await procesarBloques(
                conexionAdministracion,
                FechaNomina,
                TipoCorreo,
                mensajesCorreo,
                idProceso,
                BloquesProcesar
            );

            resolve(respuestaCorreos);
        } catch (error) {
            logging(true, `Error Proceso envio talones: Proceso: ${idProceso} `, error);
            resolve(respuestaCorreos);
        }
    });
};

const enviarCorreosBloque = (conexionAdministracion, fechaNomina, tipoCorreo, numBloque, mensajesCorreo, idProceso) => {

    return new Promise(async (resolve, reject) => {

        let respuestaCorreos = {
            Enviados: 0,
            Errores: 0,
            TimeOut: false,
            CorreosNoValidos: 0
        };

        let correo, parametros = [];

        try {

            // Configuración correo
            correo = new Correo();

            // Consulta los talones a procesar
            logging(false, `Proceso envio talones: `, `Proceso: ${idProceso} Consulta los talones a procesar del bloque ${numBloque}.`);
            parametros = [tipoCorreo, fechaNomina, numBloque];
            const consultaTalones = `
            SELECT
                num_empleado,
                nom_empleado,
                fec_nomina,
                des_correo,
                des_archivopdf,
                clv_tipo,
                clv_empresa
            FROM fun_obtienetalonesnominaporbloque($1,$2,$3)`;

            const { rows: talones } = await EjecutarConsultaParametros(conexionAdministracion, consultaTalones, parametros, true);

            // Envio de correos
            logging(false, `Proceso envio talones: `, `Proceso: ${idProceso} Inicia Envio de correos del bloque ${numBloque}.`);
            let promesas = [];
            for (const talon of talones) {
                const { clv_empresa, des_correo, des_archivopdf, fec_nomina, nom_empleado, num_empleado } = talon;

                if (regexCorreo.test(des_correo)) {
                    let mesDiaFechaTalon = moment(fec_nomina).format('MM-DD');
                    let tipoCorreoTalon = tipoCorreo;

                    tipoCorreoTalon = (mesDiaFechaTalon == process.env.FECHAUTILIDADES) ?
                        process.env.TIPOCORREOUTILIDADES : (mesDiaFechaTalon == process.env.FECHAAGUINALDO) ?
                            process.env.TIPOCORREOAGUINALDO : tipoCorreo;

                    const { des_asunto, des_saludo, des_mensaje, des_despedida, des_automatico } = mensajesCorreo.find(
                        mensaje => mensaje.clv_tiponomina == tipoCorreoTalon
                    );

                    let mensaje = '';
                    if (tipoCorreoTalon != process.env.TIPOCORREOGASOLINA) {
                        mensaje = `${des_saludo} ${nom_empleado}!<br><br>${des_mensaje} ${moment(fec_nomina).format('DD/MM/yyyy')}<br><br>${des_despedida}<br><br>${des_automatico}`;
                    } else {
                        mensaje = `${des_saludo} ${nom_empleado}!<br><br>${des_mensaje} ${moment(fec_nomina).format('DD/MM/yyyy')} al ${moment(fec_nomina).endOf('month').format('DD/MM/yyyy')}<br><br>${des_despedida}<br><br>${des_automatico}`;
                    }

                    promesas.push(
                        correo.enviarCorreo(
                            des_correo,
                            des_asunto,
                            mensaje,
                            {
                                filename: `${num_empleado}-${moment(fec_nomina).format('yyyy-MM-DD')}.pdf`,
                                content: des_archivopdf.toString('base64'),
                                encoding: 'base64'
                            },
                            {
                                clv_empresa,
                                fec_nomina: moment(fec_nomina).format('yyyy-MM-DD'),
                                num_empleado,
                                clv_tipo: tipoCorreo
                            }
                        )
                    );
                } else {
                    respuestaCorreos.CorreosNoValidos++;
                }
            }

            if (promesas.length > 0) {
                const resultados = await Promise.all(promesas);
                logging(false, `Proceso envio talones: `, `Proceso: ${idProceso} Termina envio de correos del bloque ${numBloque}.`);
                respuestaCorreos.Errores += resultados.filter((resultado) => resultado.error).length;
                respuestaCorreos.Enviados += resultados.filter((resultado) => !resultado.error).length;

                // Inserta el bloque en la tabla temporal his_talonesempleadosmail
                const correosEnviados = {
                    Empleados: {
                        Empleado: resultados.filter((resultado) => !resultado.error)
                    }
                };

                try {
                    logging(false, `Proceso envio talones: `, `Proceso: ${idProceso} guarda en his_talonesempleadosmail.`);

                    // Generar el XML y validar su estructura antes de enviarlo a la base de datos
                    const xmlData = objXml(correosEnviados);

                    // Verificar que el XML tenga una estructura básica válida
                    if (!xmlData || !xmlData.trim().startsWith('<?xml') || !xmlData.includes('<Empleados>')) {
                        throw new Error("Estructura XML no válida generada para his_talonesempleadosmail");
                    }

                    // Sanitizar el XML para prevenir inyección SQL de segundo orden
                    const sanitizedXml = xmlData.replace(/'/g, "''"); // Escapar comillas simples para PostgreSQL

                    parametros = [sanitizedXml];
                    const consultaActualizaTalonesEmpleadosEmail = `SELECT fun_actualizatalonesnominaXML($1)`;
                    await EjecutarConsultaParametros(conexionAdministracion, consultaActualizaTalonesEmpleadosEmail, parametros);
                } catch (error) {
                    logging(true, `Proceso envio talones: `, `Proceso: ${idProceso} error al guardar en his_talonesempleadosmail.`);
                    logging(false, `Proceso envio talones: `, `Proceso: ${idProceso} intenta guardar XML de los correos enviados.`);

                    // Generar el XML y validar su estructura antes de guardarlo
                    const xmlData = objXml(correosEnviados);

                    // Verificar que el XML tenga una estructura básica válida
                    if (!xmlData || !xmlData.trim().startsWith('<?xml') || !xmlData.includes('<Empleados>')) {
                        logging(true, `Proceso envio talones: `, `Proceso: ${idProceso} error al generar XML válido para guardar.`);
                    } else {
                        const xmlGuardado = GuardarXml(process.pid, xmlData);
                        if (xmlGuardado) {
                            logging(false, `Proceso envio talones: `, `Proceso: ${idProceso} XML guardado correctamente.`);
                        } else {
                            logging(true, `Proceso envio talones: `, `Proceso: ${idProceso} error al guardar XML.`);
                        }
                    }
                }
            }
            resolve(respuestaCorreos);
        } catch (error) {
            correo && correo.transporter && correo.transporter.close();
            if (error.message == mensajes.ErorTimeOutProxy) {
                respuestaCorreos.TimeOut = true;
            }
            reject({ ...respuestaCorreos, mensaje: error.message });
        }
    });
}

const procesarBloques = async (conexionAdministracion, fechaNomina, tipoCorreo, mensajesCorreo, idProceso, bloquesProcesar) => {
    let respuestaCorreos = { Enviados: 0, Errores: 0, CorreosNoValidos: 0, NumTimeOutProxy: 0, BloquesErrorTimeOut: [] };
    let reintento = 1;
    return new Promise(async (resolve) => {
        while (reintento <= process.env.NUMREINTENTOS) {
            logging(false, `Proceso envio talones: `, `Proceso: ${idProceso} reintento ${reintento} envio correos.`);
            for (const { bloque } of bloquesProcesar) {
                try {
                    const { CorreosNoValidos, Enviados, Errores } = await enviarCorreosBloque(
                        conexionAdministracion,
                        fechaNomina,
                        tipoCorreo,
                        bloque,
                        mensajesCorreo,
                        idProceso
                    );
                    respuestaCorreos.Errores += Errores;
                    respuestaCorreos.Enviados += Enviados;
                    if (reintento == 1) { respuestaCorreos.CorreosNoValidos += CorreosNoValidos };
                    bloquesProcesar = bloquesProcesar.filter(element => element.bloque != bloque);
                } catch (error) {
                    respuestaCorreos.NumTimeOutProxy++;
                    logging(true, `Error Proceso envio talones: `, `Proceso: ${idProceso} reintento ${reintento} bloque ${bloque} ${error.mensaje}`);
                }
            }
            reintento++;
        }
        setTimeout(() => { resolve({ ...respuestaCorreos, BloquesErrorTimeOut: bloquesProcesar }) }, 5000);
    });
}