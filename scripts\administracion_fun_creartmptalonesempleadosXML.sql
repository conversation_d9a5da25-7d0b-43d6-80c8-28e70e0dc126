-- Function: fun_creartmptalonesempleadosXML(xml)

-- DROP FUNCTION fun_creartmptalonesempleadosXML(xml);

CREATE OR REPLACE FUNCTION fun_creartmptalonesempleadosXML(xml)
  RETURNS void AS
$BODY$
DECLARE
	xEmpleados ALIAS FOR $1;
BEGIN
	WITH xml_data AS (
  SELECT xmlparse(document xEmpleados) AS data
	)
	INSERT INTO tmp_his_talonesempleadosmail (num_empleado, clv_empresa, clv_tipo, fec_nomina)
	SELECT
		(xpath('/Empleado/num_empleado/text()', empleado))[1]::TEXT::BIGINT AS num_empleado,
		(xpath('/Empleado/clv_empresa/text()', empleado))[1]::TEXT::INT AS clv_empresa,
		(xpath('/Empleado/clv_tipo/text()', empleado))[1]::TEXT::INT AS clv_tipo,
		(xpath('/Empleado/fec_nomina/text()', empleado))[1]::TEXT::DATE AS fec_nomina
	FROM (
		SELECT unnest(xpath('/Empleados/Empleado', data)) AS empleado
		FROM xml_data
	) query;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER;