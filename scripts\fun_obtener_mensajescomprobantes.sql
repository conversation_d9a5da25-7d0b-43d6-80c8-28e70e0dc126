-- Function: fun_obtener_mensajescomprobantes(character varying)

-- DROP FUNCTION fun_obtener_mensajescomprobantes(character varying);

CREATE OR REPLACE FUNCTION fun_obtener_mensajescomprobantes(character varying)
  RETURNS TABLE(clv_tiponomina integer, des_asunto character varying, des_saludo character varying, des_mensaje character varying, des_despedida character varying, des_automatico character varying) AS
$BODY$
  DECLARE
	cTiposNomina 	ALIAS FOR $1;
BEGIN
		RETURN QUERY
		 SELECT 
			tmc.clv_tiponomina,
			CONVERT_FROM(CONVERT(tmc.des_asunto::bytea, 'LATIN1', 'UTF8')::bytea,'SQL_ASCII')::CHARACTER VARYING,
			CONVERT_FROM(CONVERT(tmc.des_saludo::bytea, 'LATIN1', 'UTF8')::bytea,'SQL_ASCII')::CHARACTER VARYING,
			CONVERT_FROM(CONVERT(tmc.des_mensaje::bytea, 'LATIN1', 'UTF8')::bytea,'SQL_ASCII')::CHARACTER VARYING,
			CONVERT_FROM(CONVERT(tmc.des_despedida ::bytea, 'LATIN1', 'UTF8')::bytea,'SQL_ASCII')::CHARACTER VARYING,
			CONVERT_FROM(CONVERT(tmc.des_automatico ::bytea, 'LATIN1', 'UTF8')::bytea,'SQL_ASCII')::CHARACTER VARYING
		FROM mov_talonesmensajecomprobantes tmc 
		WHERE tmc.clv_tiponomina in (SELECT unnest( string_to_array(cTiposNomina, ','))::int);
		

END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;
ALTER FUNCTION fun_obtener_mensajescomprobantes(character varying)
  OWNER TO syspruebasadmon;