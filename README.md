Pasos para la instalación del componente.

1. Subir al servidor de aplicaciones el código fuente del aplicativo.
2. Dentro del server nos posicionamos dentro de la carpeta que contiene el codigo fuente e instalamos las dependencias con "**npm install**".
3. Ya instaladas las dependencias instalamas el administrador de procesos de node js para levantar el aplicativo con "**npm i -g pm2**".
4. Creamos el archivo de configuraciones para pm2 con "**pm2 init simple**".
5. Configuramos el archivo con:
```
module.exports = { 
    apps : [
        { 
            name : "api-talones", 
            script : "./build/api-envio-talones.js", 
            env: { 
                PORT: 5000, 
                NODE_ENV: 'production' 
            } 
        }
    ] 
}
(Podemos configurar el puerto que queramos);
```
6. Compilamos el aplicativo con "**npm run build**".
7. Iniciamos el aplicativo con "**pm2 start ecosystem.config.js**".
8. Guardamos la configuracion de pm2 con "**pm2 save**".
