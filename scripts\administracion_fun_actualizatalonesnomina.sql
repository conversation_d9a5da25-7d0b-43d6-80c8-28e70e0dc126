-- Function: fun_actualizatalonesnominaXML(XML)

-- DROP FUNCTION fun_actualizatalonesnominaXML(XML);

CREATE OR REPLACE FUNCTION fun_actualizatalonesnominaXML(xml)
  RETURNS void AS
$BODY$
DECLARE
	xEmpleados ALIAS FOR $1;
BEGIN

	DROP TABLE IF EXISTS tmpTalonesEmpleados;
	create temporary table tmpTalonesEmpleados (
		num_empleado integer,
		clv_empresa integer,
		clv_tipo integer,
		fec_nomina date
	);

	WITH xml_data AS (
  		SELECT xmlparse(document xEmpleados) AS data
	)
	INSERT INTO tmpTalonesEmpleados (num_empleado, clv_empresa, clv_tipo, fec_nomina)
	SELECT
		(xpath('/Empleado/num_empleado/text()', empleado))[1]::TEXT::BIGINT AS num_empleado,
		(xpath('/Empleado/clv_empresa/text()', empleado))[1]::TEXT::INT AS clv_empresa,
		(xpath('/Empleado/clv_tipo/text()', empleado))[1]::TEXT::INT AS clv_tipo,
		(xpath('/Empleado/fec_nomina/text()', empleado))[1]::TEXT::DATE AS fec_nomina
	FROM (
		SELECT unnest(xpath('/Empleados/Empleado', data)) AS empleado
		FROM xml_data
	) query;

	UPDATE his_talonesempleadosmail
	 SET clv_estatus = 1,
	     fec_envio = NOW()
	FROM tmpTalonesEmpleados 
	WHERE tmpTalonesEmpleados.num_empleado = his_talonesempleadosmail.num_empleado 
	 AND tmpTalonesEmpleados.clv_empresa = his_talonesempleadosmail.clv_empresa 
	 AND tmpTalonesEmpleados.clv_tipo = his_talonesempleadosmail.clv_tipo 
	 AND tmpTalonesEmpleados.fec_nomina = his_talonesempleadosmail.fec_nomina;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER;