import winston from 'winston';
import moment from 'moment';
import fs from 'fs';
import path from 'path';

/**
 * Función para guardar logs
 * @param {boolean} isError Indica si es error o informacion
 * @param {string} descripcion Descripción del error
 * @param {string} error Descripcion del error
 */
export const logging = (isError, descripcion, error) => {
  try {
    const level = isError ? 'error' : 'info';
    const ruta = path.resolve(`./logs/${moment().format('MMM-YYYY')}/errores/`);
    const archivo = path.join(ruta, `${moment().format('YYYY-MM-DD')}.log`);
    try {
      fs.mkdirSync(ruta, {recursive: true});
    } catch (error) {
        console.error('Error al crear el directorio:', error);
    }
    const logger = winston.createLogger({
      level,
      format: winston.format.json(),
      transports: [
        new winston.transports.File({ filename: archivo, level }),
      ],
    });
    logger.log({
      fecha: moment().format(),
      level,
      descripcion,
      message: (error || '').toString(),
    });
  } catch (error) {
    console.log(error);
  }
};

/**
 * Función para guardar logs
 * @param {string} descripcion Descripción del log del envio de correos
 */
export const guardarLogEnvio = (descripcion) => {
  try {
    const level = 'info';
    const ruta = path.resolve(`./envios/${moment().format('MMM-YYYY')}/`);
    const archivo = path.join(ruta, `${moment().format('YYYY-MM-DD')}.log`);
    fs.mkdirSync(ruta, {recursive: true});
    const logger = winston.createLogger({
      level,
      format: winston.format.json(),
      transports: [
        new winston.transports.File({ filename: archivo, level }),
      ],
    });
    logger.log({
      fecha: moment().format(),
      level,
      descripcion: (descripcion || '').toString(),
    });
  } catch (error) {
    console.log(error);
  }
};

/**
 * Guarda un XML en el sistema de archivos después de validar su estructura
 * @param {*} idProceso Identicador del proceso que ejecuta la función
 * @param {*} xmlEmpledos Xml con los datos de los empleados
 * @returns {boolean} Indica si el XML se guardó correctamente
 */
export const GuardarXml = (idProceso, xmlEmpledos) => {
  try {
    // Verificar que el XML tenga una estructura básica válida
    if (!xmlEmpledos || typeof xmlEmpledos !== 'string' || !xmlEmpledos.trim().startsWith('<?xml') || !xmlEmpledos.includes('<Empleados>')) {
      logging(true, "Error al guardar XML", "Estructura XML no válida");
      return false;
    }

    // Sanitizar el nombre del archivo para evitar path traversal
    const idProcesoSanitizado = String(idProceso).replace(/[^a-zA-Z0-9]/g, '');
    const timestamp = moment().unix();

    const ruta = path.resolve(`./logs/xml/`);
    const archivo = path.join(ruta, `${idProcesoSanitizado}-${timestamp}.log`);
    fs.mkdirSync(ruta, {recursive: true});

    fs.writeFileSync(archivo, xmlEmpledos, 'utf8');
    return true;
  } catch (error) {
    logging(true, "Error al guardar XML", error);
    return false;
  }
};