{"name": "envio_correo_nomina", "version": "1.0.0", "description": "aplicación para el envio de recibos de nómina", "main": "index.js", "scripts": {"build": "webpack", "start": "node ./build/api-envio-talones.js", "dev": "PORT=4001 NODE_ENV=\"development\" npm-run-all --parallel watch:server watch:build", "windows": "SET PORT=4001 && SET NODE_ENV=development&& npm-run-all --parallel watch:server watch:build", "watch:build": "webpack --watch", "watch:server": "nodemon --inspect=\"9229\" \"./build/api-envio-talones.js\" --watch \"./build\" ", "watch:lint": "node node_modules/eslint-watch/bin/esw -w --fix", "lint": "eslint ./ --fix"}, "author": "Gaman Solutions", "license": "ISC", "dependencies": {"@babel/runtime": "^7.27.1", "axios": "^1.9.0", "body-parser": "^2.2.0", "compression": "^1.8.0", "cors": "^2.8.5", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "iconv-lite": "^0.6.3", "mkdirp": "^3.0.1", "moment": "^2.30.1", "mssql": "^11.0.1", "nodemailer": "^7.0.3", "object-to-xml": "^2.0.0", "pg": "^8.16.0", "process": "^0.11.10", "regenerator-runtime": "^0.14.1", "tedious": "18.6.1", "winston": "^3.17.0", "worker-thread": "^1.1.0"}, "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.1", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-runtime": "^7.27.1", "@babel/preset-env": "^7.27.2", "babel-eslint": "^10.1.0", "babel-loader": "^10.0.0", "chai": "^5.2.0", "chai-http": "^5.1.2", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-watch": "^8.0.0", "husky": "^9.1.7", "nodemon": "^3.1.10", "npm-run-all": "^4.1.5", "terser-webpack-plugin": "^5.3.14", "webpack": "^5.99.9", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}, "overrides": {"serialize-javascript": "6.0.2", "ansi-regex": "5.0.1", "braces": "3.0.3", "cross-spawn": "7.0.6", "glob-parent": "6.0.2", "glob": "11.0.2", "json5": "2.2.3", "micromatch": "4.0.8", "debug": "4.4.1"}}