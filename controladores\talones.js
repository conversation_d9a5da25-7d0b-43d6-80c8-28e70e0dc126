/* eslint-disable camelcase */
import express from 'express';
import moment from 'moment';
import path from 'path';
import fs from 'fs';
import { spawn } from 'child_process';

import { crearConexionBD, EjecutarConsultaParametros } from '../configuraciones/conexionPostgres';
import { consultaConfiguracionBD } from '../configuraciones/conexionSql';
import { InsertaTemporalHisTalones } from '../utilidades/funcionesTalones';
import { ObtenerCredencialesBD } from '../utilidades/ejecutarApi';
import { logging } from '../utilidades/logs';
import { Desencriptar } from '../utilidades/encriptacion'
import Correo from '../utilidades/correo';

const router = express.Router();

// Definir el directorio base
const DIRECTORIO_BASE = path.resolve('/home/<USER>/sysx/nodejs/api-envio-talones/');

function safePathTraversal(userInput) {
  const requestedPath = path.join(DIRECTORIO_BASE, userInput);
  if (!requestedPath.startsWith(DIRECTORIO_BASE)) {
    throw new Error('Path traversal detected.');
  }
  if (path.extname(requestedPath) !== '.log') {
    throw new Error('Invalid file type. Only .log files are allowed.');
  }
  return requestedPath;
}

/**
 * Petición para procesar los talones de nómina
 */
router.get('/', async (req, res, next) => {
  try {
    //Variables
    const { query } = req;

    // Validación de tipo de dato en parámetros
    const tipoCorreo = Number.isInteger(query.tipoCorreo) ? query.tipoCorreo : 1;
    const fechaNomina = /^\d{8}$/.test(query.fechaNomina) ? query.fechaNomina : '19000101';

    let conexionConAmbiente = false;
    let respuestaProcesoEnvioTalones = { TotalTalones: 0 };
    let datos = {};

    // Consulta datos conexión a personal
    let datosConexionPersonal = {};
    
    if (process.env.HOSTPERSONAL && process.env.USERPERSONAL && process.env.CLAVEPERSONAL && process.env.BDPERSONAL && process.env.PUERTOPERSONAL) {
      // Crea el objeto de conexion a personal con las variables de ambiente
      conexionConAmbiente = true;
      datosConexionPersonal = {
        server: process.env.HOSTPERSONAL,
        user: Desencriptar(process.env.USERPERSONAL),
        password: Desencriptar(process.env.CLAVEPERSONAL),
        database: Desencriptar(process.env.BDPERSONAL),
        port: parseInt(process.env.PUERTOPERSONAL),
      }
    } else {
      // Consulta los accesos mediante la api de categorias
      const { data: datosConfiguracionBdPersonal } = await ObtenerCredencialesBD(process.env.CLAVECREDENCIALESBDPERSONAL, process.env.APLICATIONPERSONAL);
      datosConexionPersonal = {
        server: datosConfiguracionBdPersonal[0].host,
        user: datosConfiguracionBdPersonal[0].user,
        password: datosConfiguracionBdPersonal[0].pass,
        database: datosConfiguracionBdPersonal[0].db,
        port: datosConfiguracionBdPersonal[0].port,
      }
    }

    // Consulta datos conexión a administración
    logging(false, "Proceso envio talones: ", 'Consulta datos conexión a administración.');
    let datosConfiguracionBdAdministracion = {};
    if (conexionConAmbiente) {
      const datosConexionAdministracion = await consultaConfiguracionBD(datosConexionPersonal, process.env.IDACCESOSADMINISTRACION);
      datosConfiguracionBdAdministracion = {
        host: datosConexionAdministracion.IP.trim(),
        user: datosConexionAdministracion.USUARIO.trim(),
        password: datosConexionAdministracion.PASSWORD.trim(),
        database: datosConexionAdministracion.BD.trim(),
        port: process.env.PUERTOPOSTGRES,
      }
    } else {
      const { data } = await ObtenerCredencialesBD(process.env.CLAVECREDENCIALESBDADMINISTRACION);
      datosConfiguracionBdAdministracion = {
        host: data[0].host,
        user: data[0].user,
        password: data[0].pass,
        database: data[0].db,
        port: data[0].port,
      };
    }

    // Crea conexión a administración
    logging(false, "Proceso envio talones: ", 'Crea conexión a administración.');
    const conexionAdministracion = await crearConexionBD(datosConfiguracionBdAdministracion);

    // Inserta en la tabla tmp_his_talonesempleadosmail los registros que no se pudieron insertar en la ejecución anteriror.
    logging(false, "Proceso envio talones: ", 'Inicia inserción de los talones enviados que no se guadaron en la tabla tmp_his_talonesempleadosmail');
    await InsertaTemporalHisTalones(conexionAdministracion);

    // Obtiene las fechas de nómina y el número de bloques de cada fecha
    logging(false, "Proceso envio talones: ", 'Ejecuta fun_obtienetalonesnomina para mover los datos a la tabla his_talonesempleadosmail.');
    const parametrosTraspaso = [tipoCorreo, 0, '19000101'];
    const consultaTraspasoInformación = `
    select 
      num_empleado_talon,
      nom_empleado_talon,
      fec_nomina_talon,
      des_correo_talon,
      des_archivopdf_talon,
      clv_bloque_talon from fun_obtienetalonesnomina($1,$2,$3);`;

    await EjecutarConsultaParametros(conexionAdministracion, consultaTraspasoInformación, parametrosTraspaso);

    logging(false, "Proceso envio talones: ", 'Ejecuta fun_obtienetotaltalonesnomina para obtener el total de talones a enviar.');
    const parametros = [tipoCorreo, fechaNomina];
    const consultaNumBloques = `
      SELECT fun_obtienetotaltalonesnomina($1, $2) as totalTalones;`;

    const totalTalones = await EjecutarConsultaParametros(conexionAdministracion, consultaNumBloques, parametros);
    respuestaProcesoEnvioTalones.TotalTalones = totalTalones.rows[0].totaltalones;

    logging(false, "Proceso envio talones: ", 'Ejecuta fun_obtienebloquestalonesnomina para obtener los bloques a enviar.');
    const parametrosBloques = [tipoCorreo, fechaNomina];
    const consultaBloques = `
      SELECT fun_obtienebloquestalonesnomina($1, $2) as bloque;`;

    const bloques = await EjecutarConsultaParametros(conexionAdministracion, consultaBloques, parametrosBloques);

    if (bloques.rows.length === 0)
      return res.status(200).send();

    datos = {
      TipoCorreo: tipoCorreo,
      FechaNomina: fechaNomina,
      BloquesTalones: bloques.rows,
      DatosConexionAdministracion: datosConfiguracionBdAdministracion
    }

    // Configuración correo 
    const correo = new Correo();

    // Verifica si hay coexion con el servidor de correos
    logging(false, "Proceso envio talones: ", 'Verifica si hay conexion con el servidor de correos.');
    await correo.transporter.verify();
    correo.transporter.close();

    // Verifica si la ruta confiurada de la instalación de nodejs existe
    if (!fs.existsSync(process.env.RUTANODE)) {
      logging(true, "Proceso envio talones: ", `La ruta configurada para nodejs no existe: ${process.env.RUTANODE}`);
      return res.status(501).send();
    }

    logging(false, "Proceso envio talones: ", 'Inicia aplicación secundaria para procesar los talones.');

    const scriptPath = path.join(path.resolve(), 'build', 'workers.js');
    console.log('scriptPath ',scriptPath);
    const child = spawn(process.env.RUTANODE ? `${process.env.RUTANODE}node` : 'node', [scriptPath]);

    // Evento para debug de la aplicacion secundaria, solo se ejecuta cuando se corre en modo desarrollo
    child.stdout.on('data', (data) => {
      console.log('data ',data);
      try {

        if (process.env.NODE_ENV == 'development') {
          console.log(`Aplicación hija dijo: ${data}`);
        }

        if (JSON.parse(data)) {
          data = JSON.parse(data);
          console.log('data parseada ',data);
          if (data.FinProceso) {
            respuestaProcesoEnvioTalones = { ...respuestaProcesoEnvioTalones, ...data.Datos };
          }
        }

      } catch (error) { 
        console.log('error dentro del debug ',error);
      }
    });


    child.on('exit', (code) => {
      const exitCode = parseInt(code, 10);
      const codigoError = parseInt(process.env.CODIGOERROR, 10);
      const codigoParcial = parseInt(process.env.CODIGOPARCIAL, 10);

      // Sanitiza la respuesta antes de enviarla
      const safeRespuesta = JSON.stringify(respuestaProcesoEnvioTalones).replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");

      if (exitCode === codigoError) {
        return res.status(501)
        .setHeader('content-type', 'application/json')
        .setHeader('X-Content-Type-Options', 'nosniff')
        .setHeader('X-Frame-Options', 'SAMEORIGIN')
        .setHeader('X-XSS-Protection', '1; mode=block')
        .setHeader('Content-Security-Policy', "script-src 'self'; object-src 'self'; base-uri 'self'")
        .send(safeRespuesta);
      } else if (exitCode === codigoParcial) {
        return res.status(409)
        .setHeader('content-type', 'application/json')
        .setHeader('X-Content-Type-Options', 'nosniff')
        .setHeader('X-Frame-Options', 'SAMEORIGIN')
        .setHeader('X-XSS-Protection', '1; mode=block')
        .setHeader('Content-Security-Policy', "script-src 'self'; object-src 'self'; base-uri 'self'")
        .send(safeRespuesta);
      } else {
        return res.status(200)
        .setHeader('content-type', 'application/json')
        .setHeader('X-Content-Type-Options', 'nosniff')
        .setHeader('X-Frame-Options', 'SAMEORIGIN')
        .setHeader('X-XSS-Protection', '1; mode=block')
        .setHeader('Content-Security-Policy', "script-src 'self'; object-src 'self'; base-uri 'self'")
        .send(safeRespuesta);
      }
    });

    child.stdin.write(JSON.stringify(datos));
    child.stdin.end();

  } catch (error) {
    logging(true, "Error proceso envio talones: ", error);
    return res.status(501).send();
  }
});

/**
 * Petición para descargar los logs de una fecha específica
 */
router.get('/getLogs', async (req, res, next) => {
  const directorioLog = 'logs';
  const directorioError = 'errores';
  const { query: { fecha } } = req;

  try {

    if (!fecha) {
      return res.status(400).send('Fecha es requerida.');
    } else if (!moment(fecha, 'YYYY-MM-DD', true).isValid()) {
      return res.status(400).send('Fecha inválida.');
    }

    const rutaLog = `${directorioLog}/${moment(fecha).format('MMM-yyyy')}/${directorioError}/${fecha}.log`;
    const rutaSegura = safePathTraversal(rutaLog);
    if (fs.existsSync(rutaSegura)) {
      res.download(rutaSegura, (err) => {
        if (err) {
          res.status(501)
          .setHeader('content-type', 'application/json')
          .setHeader('X-Content-Type-Options', 'nosniff')
          .setHeader('X-Frame-Options', 'SAMEORIGIN')
          .setHeader('X-XSS-Protection', '1; mode=block')
          .setHeader('Content-Security-Policy', "script-src 'self'; object-src 'self'; base-uri 'self'")
          .send('Error al descargar el archivo.');
        }
      });
    } else {
      res.status(404)
      .setHeader('content-type', 'application/json')
      .setHeader('X-Content-Type-Options', 'nosniff')
      .setHeader('X-Frame-Options', 'SAMEORIGIN')
      .setHeader('X-XSS-Protection', '1; mode=block')
      .setHeader('Content-Security-Policy', "script-src 'self'; object-src 'self'; base-uri 'self'")
      .send('Archivo no encontrado.');
    }
  } catch (err) {
    res.status(403)
    .setHeader('content-type', 'application/json')
    .setHeader('X-Content-Type-Options', 'nosniff')
    .setHeader('X-Frame-Options', 'SAMEORIGIN')
    .setHeader('X-XSS-Protection', '1; mode=block')
    .setHeader('Content-Security-Policy', "script-src 'self'; object-src 'self'; base-uri 'self'")
    .send('Permiso Denegado.');
  }
});

router.get('/getVersion', async (req, res, next) => {
  return res.status(200)
  .setHeader('content-type', 'application/json')
  .setHeader('X-Content-Type-Options', 'nosniff')
  .setHeader('X-Frame-Options', 'SAMEORIGIN')
  .setHeader('X-XSS-Protection', '1; mode=block')
  .setHeader('Content-Security-Policy', "script-src 'self'; object-src 'self'; base-uri 'self'")
  .send("Version 3.3");
});

export default router;