NODE_ENV=development
PORT=50440
# Accesos personal
HOSTPERSONAL=***********
USERPERSONAL=U2FsdGVkX1/lOrf6iR/LSnOxbaOCULY9eFtPOKRw34BPIdrZxZ/kz5vBi5sf3umu
CLAVEPERSONAL=U2FsdGVkX19zwc/DjFBHFdOH3fgEmcSXwQhDEdd1R7c=
BDPERSONAL=U2FsdGVkX18inav53f5XcLifnKBe0GxOWB4p2EHrwSs=
PUERTOPERSONAL=1433
CLAVESECRETA=$@$4p13nv1od3c0rre0sC00pel$@$

# Accesos amazon
URLCORREOAMAZON=email-smtp.us-west-1.amazonaws.com
USUARIOCORREOAMAZON=AKIA2UC27GBTUG7O64MR
CLAVECORREOAMAZON=BIPPYJWQ93oCHptOP16Tatj8zOCznBdVHHHRc9WmtjKf
PUERTOCORREOAMAZON=465
CORREOSALIDAAMAZON=<EMAIL>

# sapipadmon
IDACCESOSCORREO=188
IDACCESOSADMINISTRACION=143

# configuraciones correo
PUERTOCORREO=25

# Puertos
PUERTOPOSTGRES=5432

# Ruta instalación node
RUTANODE=C:/Users/<USER>/AppData/Roaming/nvm/v16.20.1/

# Numero de nucleos con los que trabajara el proceso de envio de correos
NUMERONUCLEOS=7

# Fechas para envio de correo formato mes-dia(MM-DD)
FECHAUTILIDADES=05-20
FECHAAGUINALDO=11-20

# Tipos de correo que usa la aplicacion
TIPOCORREONOMINA=1
TIPOCORREOFONDO=2
TIPOCORREOGASOLINA=3
TIPOCORREOUTILIDADES=4
TIPOCORREOAGUINALDO=5

# Codigos de salida para aplicación secundaria
CODIGOCORRECTO=0
CODIGOERROR=1
CODIGOPARCIAL=2

# Número de correos enviados por bloque
CORREOSPORBLOQUE=100

# Claves categorias
CLAVECREDENCIALESBDADMINISTRACION=1014
CLAVECREDENCIALESBDPERSONAL=1015

# URL y variavles para obtener el token y consultar una cateoria
URLGETTOKEN=http://************:20027/authtoken/getToken
SITIBUNDUS=SISTEMAFINANZAS
UNDIVAGO=localhost
USERTYPE=1

# URL y variables para consultar una categoria
URLGETCREDENCIALES=http://************:3002/api/v1/game
CATEGORY=OBTENERCREDENCIALES
NAME=REMEDIACIONES
TYPE=OFA
APLICACION=ApiEnvioTalones
APLICATIONPERSONAL=ApiAdminSI
RUTAVALIDACION=http://localhost:20027/authtoken/validateToken

HTTP_PROXY=http://***********:8080

# Numero de reintentos para los bloques que dan timeout por el proxy
NUMREINTENTOS=5